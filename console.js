
// //点赞按钮
// var likeButton;
// //喜欢按钮
// var favoriteButton;
// try {
//     //寻找点赞按钮
//     likeButton = className("android.widget.Button").textContains("赞").findOne(6000);
//     //喜欢按钮
//     favoriteButton = className("android.widget.Button").textContains("在看").findOne(6000);
// } catch (e) {
//     //如果没找到说明不在文章页面
//     that.toast("当前不在文章页面");
// }
// setTimeout(() => {
//     likeButton.click();
//     console.log("点赞按钮被按下",likeButton.text());
//     console.log("点赞按钮被按下",likeButton.clickable());
// }, 3000);
// setTimeout(() => {
//     // 5. 检查内容区域（可滚动区域）
//     const contentView = className("WebView").findOne(500) ||
//         className("FrameLayout").scrollable(true).findOne(500);
//         if(!contentView){
//             console.log("not article page")
//         }
// }, 3000);
// 检查是否在内容页（阅读页面）- 通过时间标识判断


function isInContentPage() {
    
    try {
        // 1. 基本时间格式 - 匹配最常见的格式 "2025年04月16日 13:15"
        let basicTimePattern = /\d{4}年\d{2}月\d{2}日\s+\d{2}:\d{2}/;

        // 2. 扩展时间格式匹配 - 包括更多可能的时间格式
        let extraTimePatterns = [
            /\d{4}[-.\/年]\d{1,2}[-.\/月]\d{1,2}[\s日]+\d{1,2}:\d{1,2}/,  // 扩展的日期时间格式
            /\d{4}[-.\/]\d{1,2}[-.\/]\d{1,2}\s+\d{1,2}:\d{1,2}/,         // 2023-04-16 13:15
            /\d{2}[-.\/]\d{2}[-.\/]\d{2,4}\s+\d{1,2}:\d{1,2}/,           // 04/16/23 13:15
            /\d{2}:\d{2}[\s,]+\d{4}[-.\/年]\d{1,2}[-.\/月]\d{1,2}/       // 13:15 2023年04月16日
        ];

        // 方法1: 直接匹配最常见的时间格式
        let dateText = textMatches(basicTimePattern).findOnce();
        if (dateText != null) {
            return true;
        }

        // 方法2: 尝试扩展的时间格式匹配
        for (let i = 0; i < extraTimePatterns.length; i++) {
            let extraDateText = textMatches(extraTimePatterns[i]).findOnce();
            if (extraDateText != null) {
                return true;
            }
        }

        // 方法3: 尝试查找包含年月日的文本
        let dateElements = textContains("年").find();
        if (dateElements && dateElements.length > 0) {
            for (let i = 0; i < dateElements.length; i++) {
                let text = dateElements[i].text();
                if (text.match(/\d{4}年/) && (text.includes("月") || text.includes("日"))) {
                    return true;
                }
            }
        }

        // 方法4: 尝试检测时间格式 (HH:MM)
        let timeElements = textMatches(/\d{2}:\d{2}/).find();
        if (timeElements && timeElements.length > 0) {
            // 检查当前页面是否有其他确认为内容页的元素
            let monthElement = textContains("月").findOnce();
            let dayElement = textContains("日").findOnce();

            if (monthElement != null && dayElement != null) {
                return true;
            }

        }

        // 调试: 输出页面上可能与时间相关的文本
        console.log("🔍 调试: 搜索页面上可能的时间相关文本");
        let possibleDateTexts = [];
        let allTexts = textMatches(/\d+/).find();
        if (allTexts && allTexts.length > 0) {
            for (let i = 0; i < Math.min(allTexts.length, 10); i++) {
                let text = allTexts[i].text();
                if (text.length > 4 && (text.includes(":") || text.includes("年") || text.includes("月") || text.includes("日"))) {
                    possibleDateTexts.push(text);
                }
            }
        }
        return false;
    } catch (e) {
        return false;
    }
}

